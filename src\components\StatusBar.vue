<template>
  <div class="status-bar">
    <div class="status-info">
      <span class="file-count">
        文件总数: {{ fileStats.total }}
      </span>
      <span class="file-types">
        图片: {{ fileStats.images }} | 
        PDF: {{ fileStats.pdfs }} | 
        Word: {{ fileStats.words }}
      </span>
      <span class="total-size">
        总大小: {{ formatFileSize(fileStats.totalSize) }}
      </span>
    </div>
    
    <div class="current-file" v-if="selectedFile">
      <span class="selected-file-info">
        当前文件: {{ selectedFile.name }} 
        ({{ formatFileSize(selectedFile.size) }})
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useFileStore } from '../stores/fileStore'

const props = defineProps({
  selectedFile: {
    type: Object,
    default: null
  }
})

const fileStore = useFileStore()

const fileStats = computed(() => fileStore.getFileStats())

const formatFileSize = (bytes) => {
  return fileStore.formatFileSize(bytes)
}
</script>

<style scoped>
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #ebeef5;
  font-size: 12px;
  color: #606266;
}

.status-info {
  display: flex;
  gap: 16px;
}

.current-file {
  font-weight: 500;
  color: #409eff;
}
</style>
