<template>
  <div class="watermark-editor">
    <div class="editor-header">
      <h3>水印设置</h3>
      <el-button type="primary" @click="applyWatermark" :loading="processing"> 应用水印 </el-button>
    </div>

    <div class="editor-content">
      <el-form :model="watermarkConfig" label-width="100px">
        <el-form-item label="水印类型">
          <el-radio-group v-model="watermarkConfig.type">
            <el-radio value="text">文字水印</el-radio>
            <el-radio value="image">图片水印</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 文字水印设置 -->
        <template v-if="watermarkConfig.type === 'text'">
          <el-form-item label="水印文字">
            <el-input v-model="watermarkConfig.text" placeholder="请输入水印文字"></el-input>
          </el-form-item>

          <el-form-item label="字体大小">
            <el-slider
              v-model="watermarkConfig.fontSize"
              :min="12"
              :max="72"
              show-input
            ></el-slider>
          </el-form-item>

          <el-form-item label="字体颜色">
            <el-color-picker v-model="watermarkConfig.color"></el-color-picker>
          </el-form-item>

          <el-form-item label="透明度">
            <el-slider
              v-model="watermarkConfig.opacity"
              :min="0"
              :max="1"
              :step="0.1"
              show-input
            ></el-slider>
          </el-form-item>
        </template>

        <!-- 图片水印设置 -->
        <template v-if="watermarkConfig.type === 'image'">
          <el-form-item label="水印图片">
            <el-upload
              :auto-upload="false"
              :on-change="handleWatermarkImageSelect"
              :show-file-list="false"
              accept="image/*"
            >
              <el-button>选择图片</el-button>
            </el-upload>
            <div v-if="watermarkConfig.imagePreview" class="image-preview">
              <img :src="watermarkConfig.imagePreview" alt="水印预览" />
            </div>
          </el-form-item>

          <el-form-item label="图片大小">
            <el-slider
              v-model="watermarkConfig.imageSize"
              :min="50"
              :max="500"
              show-input
            ></el-slider>
          </el-form-item>

          <el-form-item label="透明度">
            <el-slider
              v-model="watermarkConfig.opacity"
              :min="0"
              :max="1"
              :step="0.1"
              show-input
            ></el-slider>
          </el-form-item>
        </template>

        <!-- 通用设置 -->
        <el-form-item label="位置">
          <el-select v-model="watermarkConfig.position" placeholder="选择水印位置">
            <el-option label="左上角" value="top-left"></el-option>
            <el-option label="上方居中" value="top-center"></el-option>
            <el-option label="右上角" value="top-right"></el-option>
            <el-option label="左侧居中" value="middle-left"></el-option>
            <el-option label="居中" value="center"></el-option>
            <el-option label="右侧居中" value="middle-right"></el-option>
            <el-option label="左下角" value="bottom-left"></el-option>
            <el-option label="下方居中" value="bottom-center"></el-option>
            <el-option label="右下角" value="bottom-right"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="旋转角度">
          <el-slider v-model="watermarkConfig.rotation" :min="-45" :max="45" show-input></el-slider>
        </el-form-item>
      </el-form>
    </div>

    <!-- 预览区域 -->
    <div class="preview-section" v-if="selectedFile">
      <h4>预览</h4>
      <div class="preview-container">
        <canvas ref="previewCanvas" class="preview-canvas"></canvas>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  selectedFile: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['watermark-applied'])

// 响应式数据
const processing = ref(false)
const previewCanvas = ref()

const watermarkConfig = reactive({
  type: 'text',
  text: '水印文字',
  fontSize: 24,
  color: '#000000',
  opacity: 0.5,
  position: 'bottom-right',
  rotation: 0,
  imageFile: null,
  imagePreview: null,
  imageSize: 100,
})

// 监听选中文件变化
watch(
  () => props.selectedFile,
  (newFile) => {
    if (newFile) {
      nextTick(() => {
        updatePreview()
      })
    }
  },
  { immediate: true },
)

// 监听水印配置变化
watch(
  watermarkConfig,
  () => {
    if (props.selectedFile) {
      updatePreview()
    }
  },
  { deep: true },
)

// 方法
const handleWatermarkImageSelect = (file) => {
  watermarkConfig.imageFile = file.raw

  const reader = new FileReader()
  reader.onload = (e) => {
    watermarkConfig.imagePreview = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

const updatePreview = async () => {
  if (!props.selectedFile || !previewCanvas.value) return

  const canvas = previewCanvas.value
  const ctx = canvas.getContext('2d')

  // 根据文件类型处理预览
  if (props.selectedFile.type === 'image') {
    await previewImageWithWatermark(canvas, ctx)
  } else {
    // 对于PDF和Word文件，显示占位符
    showPlaceholderPreview(canvas, ctx)
  }
}

const previewImageWithWatermark = async (canvas, ctx) => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      // 设置画布大小
      const maxWidth = 400
      const maxHeight = 300
      const ratio = Math.min(maxWidth / img.width, maxHeight / img.height)

      canvas.width = img.width * ratio
      canvas.height = img.height * ratio

      // 绘制原图
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

      // 绘制水印
      drawWatermark(ctx, canvas.width, canvas.height)
      resolve()
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      img.src = e.target.result
    }
    reader.readAsDataURL(props.selectedFile.file)
  })
}

const showPlaceholderPreview = (canvas, ctx) => {
  canvas.width = 400
  canvas.height = 300

  // 绘制占位符背景
  ctx.fillStyle = '#f5f5f5'
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  // 绘制文件类型图标
  ctx.fillStyle = '#909399'
  ctx.font = '48px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(props.selectedFile.type.toUpperCase(), canvas.width / 2, canvas.height / 2 - 20)

  ctx.font = '16px Arial'
  ctx.fillText('预览不可用，请导出查看效果', canvas.width / 2, canvas.height / 2 + 20)

  // 绘制水印预览
  drawWatermark(ctx, canvas.width, canvas.height)
}

const drawWatermark = (ctx, width, height) => {
  ctx.save()

  // 计算水印位置
  const position = calculateWatermarkPosition(width, height)

  // 设置透明度
  ctx.globalAlpha = watermarkConfig.opacity

  // 移动到水印位置
  ctx.translate(position.x, position.y)

  // 旋转
  ctx.rotate((watermarkConfig.rotation * Math.PI) / 180)

  if (watermarkConfig.type === 'text') {
    // 绘制文字水印
    ctx.fillStyle = watermarkConfig.color
    ctx.font = `${watermarkConfig.fontSize}px Arial`
    ctx.textAlign = 'center'
    ctx.fillText(watermarkConfig.text, 0, 0)
  } else if (watermarkConfig.type === 'image' && watermarkConfig.imagePreview) {
    // 绘制图片水印
    const img = new Image()
    img.onload = () => {
      const size = watermarkConfig.imageSize
      ctx.drawImage(img, -size / 2, -size / 2, size, size)
    }
    img.src = watermarkConfig.imagePreview
  }

  ctx.restore()
}

const calculateWatermarkPosition = (width, height) => {
  const positions = {
    'top-left': { x: 50, y: 50 },
    'top-center': { x: width / 2, y: 50 },
    'top-right': { x: width - 50, y: 50 },
    'middle-left': { x: 50, y: height / 2 },
    center: { x: width / 2, y: height / 2 },
    'middle-right': { x: width - 50, y: height / 2 },
    'bottom-left': { x: 50, y: height - 50 },
    'bottom-center': { x: width / 2, y: height - 50 },
    'bottom-right': { x: width - 50, y: height - 50 },
  }

  return positions[watermarkConfig.position] || positions.center
}

const applyWatermark = async () => {
  if (!props.selectedFile) return

  processing.value = true

  try {
    // 根据文件类型应用水印
    let result
    if (props.selectedFile.type === 'image') {
      result = await applyImageWatermark()
    } else if (props.selectedFile.type === 'pdf') {
      result = await applyPdfWatermark()
    } else if (props.selectedFile.type === 'word') {
      result = await applyWordWatermark()
    }

    emit('watermark-applied', result)

    // 显示成功消息
    ElMessage.success('水印添加成功！')
  } catch (error) {
    console.error('应用水印失败:', error)

    // 显示错误消息
    ElMessage.error(error.message || '水印添加失败，请重试')
  } finally {
    processing.value = false
  }
}

const applyImageWatermark = async () => {
  try {
    const { ImageWatermarkProcessor, FileDownloader } = await import('../utils/watermarkUtils')

    const blob = await ImageWatermarkProcessor.addWatermark(
      props.selectedFile.file,
      watermarkConfig,
    )

    return {
      blob,
      filename: FileDownloader.generateWatermarkedFilename(props.selectedFile.name),
      type: 'image',
    }
  } catch (error) {
    console.error('图片水印处理失败:', error)
    throw error
  }
}

const applyPdfWatermark = async () => {
  try {
    const { PDFWatermarkProcessor, FileDownloader } = await import('../utils/watermarkUtils')

    const blob = await PDFWatermarkProcessor.addWatermark(props.selectedFile.file, watermarkConfig)

    return {
      blob,
      filename: FileDownloader.generateWatermarkedFilename(props.selectedFile.name),
      type: 'pdf',
    }
  } catch (error) {
    console.error('PDF水印处理失败:', error)
    throw error
  }
}

const applyWordWatermark = async () => {
  try {
    const { WordWatermarkProcessor, FileDownloader } = await import('../utils/watermarkUtils')

    const blob = await WordWatermarkProcessor.addWatermark(props.selectedFile.file, watermarkConfig)

    return {
      blob,
      filename: FileDownloader.generateWatermarkedFilename(props.selectedFile.name),
      type: 'word',
    }
  } catch (error) {
    console.error('Word水印处理失败:', error)
    throw error
  }
}
</script>

<style scoped>
.watermark-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.editor-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.image-preview {
  margin-top: 8px;
}

.image-preview img {
  max-width: 100px;
  max-height: 100px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.preview-section {
  padding: 16px;
  border-top: 1px solid #ebeef5;
}

.preview-container {
  margin-top: 8px;
  text-align: center;
}

.preview-canvas {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  max-width: 100%;
}
</style>
