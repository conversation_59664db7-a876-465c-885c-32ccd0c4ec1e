# 测试指南

## 问题修复说明

### PDF加密文件处理

我们已经修复了PDF加密文件的处理问题：

1. **自动检测加密PDF**: 系统会自动检测加密的PDF文件
2. **智能处理**: 尝试使用 `ignoreEncryption: true` 选项加载加密PDF
3. **友好错误提示**: 提供清晰的错误信息和解决建议
4. **文件验证**: 在上传时预先验证文件，提前发现问题

### PDF中文字符编码问题

我们已经修复了PDF文字水印中文字符编码问题：

1. **自动检测中文**: 系统会自动检测水印文字中是否包含中文字符
2. **智能转换**: 中文字符自动转换为图片水印，避免编码错误
3. **保持样式**: 转换后的图片水印保持原有的字体大小、颜色、透明度等设置
4. **用户提示**: 界面会显示友好提示，告知用户中文字符的处理方式
5. **无缝体验**: 用户无需手动操作，系统自动处理所有编码问题

### 新增功能

1. **文件验证系统**
   - 文件类型检查
   - 文件大小限制（50MB）
   - PDF文件完整性检查
   - 加密PDF警告提示

2. **用户体验改进**
   - 成功/错误消息提示
   - 上传进度反馈
   - 详细的帮助文档
   - 状态栏显示文件统计

3. **帮助系统**
   - 支持的文件格式说明
   - PDF加密处理指南
   - 水印设置技巧
   - 常见问题解答

## 测试步骤

### 1. 基础功能测试

```bash
# 启动开发服务器
npm run dev
```

访问 `http://localhost:5173` 进行测试

### 2. 文件上传测试

**测试用例 1: 正常文件**

- 上传JPG/PNG图片文件
- 上传普通PDF文件
- 上传Word文档(.docx)
- 验证文件是否正确显示在文件树中

**测试用例 2: 加密PDF文件**

- 上传加密的PDF文件
- 观察是否显示警告信息
- 尝试添加水印，验证是否能正常处理

**测试用例 3: 不支持的文件**

- 上传.txt文件
- 上传.xlsx文件
- 验证是否显示错误提示

**测试用例 4: 大文件**

- 上传超过50MB的文件
- 验证是否显示大小限制错误

**测试用例 5: 中文字符水印**

- 选择PDF文件
- 设置包含中文字符的文字水印（如"机密文件"）
- 观察是否显示中文字符提示信息
- 应用水印并验证处理结果
- 检查导出的PDF中水印是否正确显示

### 3. 水印功能测试

**图片水印测试**

- 选择图片文件
- 设置文字水印，调整各种参数
- 设置图片水印
- 验证预览效果
- 导出并检查结果

**PDF水印测试**

- 选择PDF文件（包括加密PDF）
- 添加文字水印
- 添加图片水印
- 导出并用PDF阅读器检查结果

**Word水印测试**

- 选择Word文档
- 添加水印
- 导出并用Word打开检查

### 4. 界面功能测试

**文件管理**

- 创建文件夹
- 文件分类
- 文件选择

**状态栏**

- 文件统计信息
- 当前选中文件信息

**帮助系统**

- 点击"使用帮助"按钮
- 浏览各个帮助章节
- 验证信息是否准确

### 5. 错误处理测试

**网络错误**

- 断网情况下的行为

**文件损坏**

- 上传损坏的PDF文件
- 上传损坏的图片文件

**浏览器兼容性**

- Chrome浏览器测试
- Firefox浏览器测试
- Safari浏览器测试（如果可用）

## 预期结果

### 成功场景

- 文件上传成功，显示绿色成功消息
- 水印添加成功，自动下载处理后的文件
- 加密PDF显示警告但能正常处理
- 界面响应流畅，无明显卡顿

### 错误场景

- 不支持的文件显示红色错误消息
- 文件过大显示大小限制错误
- 严重加密的PDF显示友好错误信息
- 网络错误时显示相应提示

## 性能测试

### 文件处理性能

- 小图片（<1MB）: 应在1秒内完成
- 大图片（5-10MB）: 应在5秒内完成
- 普通PDF（<5MB）: 应在10秒内完成
- 大PDF（20-50MB）: 可能需要30秒以上

### 内存使用

- 监控浏览器内存使用情况
- 处理大文件后检查内存是否正常释放

## 已知限制

1. **Word文档处理**: 由于docx库限制，Word水印功能相对简单
2. **PDF加密**: 某些高级加密的PDF可能无法处理
3. **文件大小**: 超大文件可能导致浏览器卡顿
4. **浏览器兼容性**: 需要现代浏览器支持

## 故障排除

如果遇到问题：

1. 检查浏览器控制台错误信息
2. 确认文件格式和大小符合要求
3. 尝试刷新页面重新操作
4. 检查网络连接状态
5. 尝试使用不同的浏览器

## 反馈

测试过程中发现的问题请记录：

- 问题描述
- 重现步骤
- 预期结果
- 实际结果
- 浏览器版本和操作系统信息
