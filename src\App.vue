<script setup>
import { ref } from 'vue'
import FileManager from './components/FileManager.vue'
import WatermarkEditor from './components/WatermarkEditor.vue'
import StatusBar from './components/StatusBar.vue'
import { useFileStore } from './stores/fileStore'

const fileStore = useFileStore()
const selectedFile = ref(null)

const handleFileSelected = (file) => {
  selectedFile.value = file
  fileStore.selectFile(file)
}

const handleWatermarkApplied = (result) => {
  // 处理水印应用结果
  console.log('水印已应用:', result)

  // 下载文件
  const link = document.createElement('a')
  link.href = URL.createObjectURL(result.blob)
  link.download = result.filename
  link.click()

  // 清理URL对象
  URL.revokeObjectURL(link.href)
}
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>水印系统</h1>
      <p>支持图片、PDF、Word文档的水印添加</p>
    </header>

    <main class="app-main">
      <div class="sidebar">
        <FileManager @file-selected="handleFileSelected" />
      </div>

      <div class="content">
        <WatermarkEditor
          :selected-file="selectedFile"
          @watermark-applied="handleWatermarkApplied"
        />
      </div>
    </main>

    <StatusBar :selected-file="selectedFile" />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f5f5f5;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-size: 2rem;
  margin-bottom: 8px;
}

.app-header p {
  opacity: 0.9;
  font-size: 1rem;
}

.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 350px;
  background: white;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

/* Element Plus 样式覆盖 */
.el-tree-node__content {
  height: 40px;
}

.el-upload-dragger {
  width: 100%;
}
</style>
