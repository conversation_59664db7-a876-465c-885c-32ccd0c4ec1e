import { PDFDocument, rgb } from 'pdf-lib'
import { Document, Packer, Paragraph, TextRun, ImageRun } from 'docx'
import { saveAs } from 'file-saver'

/**
 * 图片水印处理工具
 */
export class ImageWatermarkProcessor {
  static async addWatermark(imageFile, watermarkConfig) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        try {
          canvas.width = img.width
          canvas.height = img.height

          // 绘制原图
          ctx.drawImage(img, 0, 0)

          // 绘制水印
          this.drawWatermark(ctx, canvas.width, canvas.height, watermarkConfig)

          // 转换为Blob
          canvas.toBlob((blob) => {
            resolve(blob)
          }, 'image/png')
        } catch (error) {
          reject(error)
        }
      }

      img.onerror = reject

      const reader = new FileReader()
      reader.onload = (e) => {
        img.src = e.target.result
      }
      reader.readAsDataURL(imageFile)
    })
  }

  static drawWatermark(ctx, width, height, config) {
    ctx.save()

    // 计算水印位置
    const position = this.calculatePosition(width, height, config.position)

    // 设置透明度
    ctx.globalAlpha = config.opacity

    // 移动到水印位置
    ctx.translate(position.x, position.y)

    // 旋转
    ctx.rotate((config.rotation * Math.PI) / 180)

    if (config.type === 'text') {
      // 绘制文字水印
      ctx.fillStyle = config.color
      ctx.font = `${config.fontSize}px Arial`
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText(config.text, 0, 0)
    } else if (config.type === 'image' && config.imageFile) {
      // 绘制图片水印
      const img = new Image()
      img.onload = () => {
        const size = config.imageSize
        ctx.drawImage(img, -size / 2, -size / 2, size, size)
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        img.src = e.target.result
      }
      reader.readAsDataURL(config.imageFile)
    }

    ctx.restore()
  }

  static calculatePosition(width, height, position) {
    const margin = 50
    const positions = {
      'top-left': { x: margin, y: margin },
      'top-center': { x: width / 2, y: margin },
      'top-right': { x: width - margin, y: margin },
      'middle-left': { x: margin, y: height / 2 },
      center: { x: width / 2, y: height / 2 },
      'middle-right': { x: width - margin, y: height / 2 },
      'bottom-left': { x: margin, y: height - margin },
      'bottom-center': { x: width / 2, y: height - margin },
      'bottom-right': { x: width - margin, y: height - margin },
    }

    return positions[position] || positions.center
  }
}

/**
 * PDF水印处理工具
 */
export class PDFWatermarkProcessor {
  static async addWatermark(pdfFile, watermarkConfig) {
    try {
      const arrayBuffer = await pdfFile.arrayBuffer()

      // 尝试加载PDF，如果加密则使用ignoreEncryption选项
      let pdfDoc
      try {
        pdfDoc = await PDFDocument.load(arrayBuffer)
      } catch (encryptionError) {
        if (encryptionError.message.includes('encrypted')) {
          console.warn('检测到加密PDF，尝试忽略加密加载...')
          pdfDoc = await PDFDocument.load(arrayBuffer, { ignoreEncryption: true })
        } else {
          throw encryptionError
        }
      }

      const pages = pdfDoc.getPages()

      for (const page of pages) {
        const { width, height } = page.getSize()

        if (watermarkConfig.type === 'text') {
          await this.addTextWatermark(page, width, height, watermarkConfig, pdfDoc)
        } else if (watermarkConfig.type === 'image') {
          await this.addImageWatermark(pdfDoc, page, width, height, watermarkConfig)
        }
      }

      const pdfBytes = await pdfDoc.save()
      return new Blob([pdfBytes], { type: 'application/pdf' })
    } catch (error) {
      console.error('PDF水印处理失败:', error)

      // 提供更友好的错误信息
      if (error.message.includes('encrypted')) {
        throw new Error('此PDF文件已加密，无法添加水印。请先解除PDF的密码保护。')
      } else if (error.message.includes('Invalid PDF')) {
        throw new Error('无效的PDF文件格式，请检查文件是否损坏。')
      } else {
        throw new Error(`PDF处理失败: ${error.message}`)
      }
    }
  }

  static async addTextWatermark(page, width, height, config, pdfDoc) {
    const position = ImageWatermarkProcessor.calculatePosition(width, height, config.position)

    try {
      // 检查是否包含中文字符
      const hasChinese = /[\u4e00-\u9fa5]/.test(config.text)

      if (hasChinese) {
        // 对于中文字符，尝试嵌入支持Unicode的字体
        try {
          // 使用系统默认字体或嵌入字体
          const font = await pdfDoc.embedFont('Helvetica') // 先尝试基础字体

          page.drawText(config.text, {
            x: position.x,
            y: height - position.y,
            size: config.fontSize,
            font: font,
            color: this.hexToRgb(config.color),
            opacity: config.opacity,
            rotate: { angle: config.rotation },
          })
        } catch (fontError) {
          // 如果字体嵌入失败，转换为图片水印
          console.warn('中文字体嵌入失败，转换为图片水印...')
          await this.addTextAsImageWatermark(page, width, height, config, pdfDoc)
        }
      } else {
        // 英文字符使用默认处理
        page.drawText(config.text, {
          x: position.x,
          y: height - position.y,
          size: config.fontSize,
          color: this.hexToRgb(config.color),
          opacity: config.opacity,
          rotate: { angle: config.rotation },
        })
      }
    } catch (encodingError) {
      if (encodingError.message.includes('cannot encode')) {
        console.warn('字符编码失败，转换为图片水印...')
        await this.addTextAsImageWatermark(page, width, height, config, pdfDoc)
      } else {
        throw encodingError
      }
    }
  }

  // 将文字转换为图片水印的方法
  static async addTextAsImageWatermark(page, width, height, config, pdfDoc) {
    try {
      // 创建Canvas来生成文字图片
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      // 设置Canvas大小
      const textWidth = config.text.length * config.fontSize * 0.6
      const textHeight = config.fontSize * 1.2
      canvas.width = textWidth
      canvas.height = textHeight

      // 绘制文字
      ctx.fillStyle = config.color
      ctx.font = `${config.fontSize}px Arial, "Microsoft YaHei", "SimHei", sans-serif`
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.globalAlpha = config.opacity

      // 应用旋转
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.rotate((config.rotation * Math.PI) / 180)
      ctx.fillText(config.text, 0, 0)

      // 转换为PNG数据
      const imageData = canvas.toDataURL('image/png')
      const imageBytes = this.dataURLToUint8Array(imageData)

      // 嵌入到PDF中
      const image = await pdfDoc.embedPng(imageBytes)
      const position = ImageWatermarkProcessor.calculatePosition(width, height, config.position)

      page.drawImage(image, {
        x: position.x - textWidth / 2,
        y: height - position.y - textHeight / 2,
        width: textWidth,
        height: textHeight,
        opacity: config.opacity,
      })
    } catch (error) {
      console.error('文字转图片水印失败:', error)
      throw new Error('文字水印处理失败，请尝试使用图片水印')
    }
  }

  // 辅助方法：将DataURL转换为Uint8Array
  static dataURLToUint8Array(dataURL) {
    const base64 = dataURL.split(',')[1]
    const binaryString = atob(base64)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    return bytes
  }

  static async addImageWatermark(pdfDoc, page, width, height, config) {
    if (!config.imageFile) return

    try {
      const imageBytes = await config.imageFile.arrayBuffer()
      const image = await pdfDoc.embedPng(imageBytes)

      const position = ImageWatermarkProcessor.calculatePosition(width, height, config.position)
      const size = config.imageSize

      page.drawImage(image, {
        x: position.x - size / 2,
        y: height - position.y - size / 2,
        width: size,
        height: size,
        opacity: config.opacity,
        rotate: { angle: config.rotation },
      })
    } catch (error) {
      console.error('PDF图片水印添加失败:', error)
    }
  }

  static hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? rgb(
          parseInt(result[1], 16) / 255,
          parseInt(result[2], 16) / 255,
          parseInt(result[3], 16) / 255,
        )
      : rgb(0, 0, 0)
  }
}

/**
 * Word文档水印处理工具
 */
export class WordWatermarkProcessor {
  static async addWatermark(wordFile, watermarkConfig) {
    try {
      // 注意：docx库主要用于创建新文档，修改现有文档较复杂
      // 这里提供一个基础实现，实际项目中可能需要更复杂的处理

      const arrayBuffer = await wordFile.arrayBuffer()

      // 创建新文档并添加水印
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: `原文档内容 + 水印: ${watermarkConfig.text}`,
                    size: 24,
                  }),
                ],
              }),
            ],
          },
        ],
      })

      const blob = await Packer.toBlob(doc)
      return blob
    } catch (error) {
      console.error('Word水印处理失败:', error)
      throw error
    }
  }
}

/**
 * 文件下载工具
 */
export class FileDownloader {
  static download(blob, filename) {
    saveAs(blob, filename)
  }

  static getFileExtension(filename) {
    return filename.split('.').pop().toLowerCase()
  }

  static generateWatermarkedFilename(originalFilename) {
    const ext = this.getFileExtension(originalFilename)
    const nameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'))
    return `${nameWithoutExt}_watermarked.${ext}`
  }
}

/**
 * 文件类型检测工具
 */
export class FileTypeDetector {
  static getFileType(filename) {
    const ext = filename.toLowerCase().split('.').pop()

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
      return 'image'
    } else if (ext === 'pdf') {
      return 'pdf'
    } else if (['doc', 'docx'].includes(ext)) {
      return 'word'
    }

    return 'unknown'
  }

  static isSupported(filename) {
    return this.getFileType(filename) !== 'unknown'
  }

  static getSupportedExtensions() {
    return [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'bmp',
      'webp', // 图片
      'pdf', // PDF
      'doc',
      'docx', // Word
    ]
  }

  static async validateFile(file) {
    const errors = []

    // 检查文件类型
    if (!this.isSupported(file.name)) {
      errors.push(`不支持的文件类型: ${file.name.split('.').pop()}`)
    }

    // 检查文件大小 (50MB限制)
    const maxSize = 50 * 1024 * 1024
    if (file.size > maxSize) {
      errors.push(`文件过大: ${(file.size / 1024 / 1024).toFixed(2)}MB，最大支持50MB`)
    }

    // 检查文件是否为空
    if (file.size === 0) {
      errors.push('文件为空')
    }

    // 对于PDF文件，尝试检查是否可以读取
    if (this.getFileType(file.name) === 'pdf') {
      try {
        const arrayBuffer = await file.arrayBuffer()
        const { PDFDocument } = await import('pdf-lib')

        // 尝试加载PDF
        try {
          await PDFDocument.load(arrayBuffer)
        } catch (encryptionError) {
          if (encryptionError.message.includes('encrypted')) {
            // 加密PDF可以处理，但给出警告
            errors.push('警告: 检测到加密PDF，可能影响水印效果')
          } else {
            errors.push('PDF文件格式错误或已损坏')
          }
        }
      } catch (error) {
        errors.push('PDF文件验证失败')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: errors.filter((e) => e.startsWith('警告')),
    }
  }
}
