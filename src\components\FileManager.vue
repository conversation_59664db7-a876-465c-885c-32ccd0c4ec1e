<template>
  <div class="file-manager">
    <div class="toolbar">
      <el-button type="primary" @click="showUploadDialog = true">
        <el-icon><Upload /></el-icon>
        上传文件
      </el-button>
      <el-button @click="showCreateFolderDialog = true">
        <el-icon><FolderAdd /></el-icon>
        新建文件夹
      </el-button>
    </div>

    <div class="file-tree">
      <el-tree
        :data="fileTree"
        :props="treeProps"
        node-key="id"
        @node-click="handleNodeClick"
        :expand-on-click-node="false"
      >
        <template #default="{ node, data }">
          <span class="tree-node">
            <el-icon v-if="data.type === 'folder'"><Folder /></el-icon>
            <el-icon v-else-if="data.type === 'image'"><Picture /></el-icon>
            <el-icon v-else-if="data.type === 'pdf'"><Document /></el-icon>
            <el-icon v-else-if="data.type === 'word'"><Document /></el-icon>
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>

    <!-- 上传文件对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文件" width="500px">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :on-change="handleFileSelect"
        :accept="acceptedFileTypes"
        drag
        multiple
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">支持 jpg/png/gif/pdf/docx 格式文件</div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpload">确定</el-button>
      </template>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog v-model="showCreateFolderDialog" title="新建文件夹" width="400px">
      <el-form :model="folderForm" label-width="80px">
        <el-form-item label="文件夹名">
          <el-input v-model="folderForm.name" placeholder="请输入文件夹名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="createFolder">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Upload, FolderAdd, Folder, Picture, Document, UploadFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useFileStore } from '../stores/fileStore'
import { FileTypeDetector } from '../utils/watermarkUtils'

const emit = defineEmits(['file-selected'])
const fileStore = useFileStore()

// 响应式数据
const showUploadDialog = ref(false)
const showCreateFolderDialog = ref(false)
const uploadRef = ref()
const selectedFiles = ref([])

const folderForm = reactive({
  name: '',
})

// 使用store中的文件树
const fileTree = computed(() => fileStore.fileTree)

const treeProps = {
  children: 'children',
  label: 'label',
}

const acceptedFileTypes = '.jpg,.jpeg,.png,.gif,.pdf,.docx,.doc'

// 方法
const handleFileSelect = (file) => {
  selectedFiles.value.push(file)
}

const handleUpload = async () => {
  if (selectedFiles.value.length === 0) {
    return
  }

  let successCount = 0
  let errorCount = 0

  for (const file of selectedFiles.value) {
    try {
      // 验证文件
      const validation = await FileTypeDetector.validateFile(file.raw)

      if (!validation.isValid) {
        const errors = validation.errors.filter((e) => !e.startsWith('警告'))
        if (errors.length > 0) {
          ElMessage.error(`${file.name}: ${errors.join(', ')}`)
          errorCount++
          continue
        }
      }

      // 显示警告（如果有）
      if (validation.warnings.length > 0) {
        ElMessage.warning(`${file.name}: ${validation.warnings.join(', ')}`)
      }

      const fileType = FileTypeDetector.getFileType(file.name)
      const newFile = fileStore.addFile({
        name: file.name,
        type: fileType,
        file: file.raw,
        size: file.size,
      })

      successCount++
    } catch (error) {
      console.error(`文件验证失败: ${file.name}`, error)
      ElMessage.error(`${file.name}: 文件处理失败`)
      errorCount++
    }
  }

  // 显示结果摘要
  if (successCount > 0) {
    ElMessage.success(`成功上传 ${successCount} 个文件`)
  }
  if (errorCount > 0) {
    ElMessage.error(`${errorCount} 个文件上传失败`)
  }

  selectedFiles.value = []
  showUploadDialog.value = false
}

const createFolder = () => {
  if (!folderForm.name.trim()) {
    return
  }

  fileStore.addFolder(folderForm.name)
  folderForm.name = ''
  showCreateFolderDialog.value = false
}

const handleNodeClick = (data) => {
  if (data.type !== 'folder' && data.file) {
    emit('file-selected', data.file)
  }
}
</script>

<style scoped>
.file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.file-tree {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}
</style>
