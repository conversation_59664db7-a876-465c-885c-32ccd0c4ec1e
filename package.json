{"name": "watermarking-system-packge", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "pdf-lib": "^1.17.1", "docx": "^8.5.0", "mammoth": "^1.6.0", "file-saver": "^2.0.5", "jszip": "^3.10.1", "element-plus": "^2.8.8", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "prettier": "3.6.2", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}