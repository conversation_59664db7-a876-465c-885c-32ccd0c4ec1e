import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useFileStore = defineStore('file', () => {
  // 状态
  const files = ref([])
  const folders = ref([
    {
      id: 'root',
      name: '我的文件',
      parentId: null,
      children: [],
      type: 'folder'
    }
  ])
  const selectedFile = ref(null)
  const currentFolder = ref('root')

  // 计算属性
  const currentFolderFiles = computed(() => {
    return files.value.filter(file => file.folderId === currentFolder.value)
  })

  const currentFolderSubfolders = computed(() => {
    return folders.value.filter(folder => folder.parentId === currentFolder.value)
  })

  const fileTree = computed(() => {
    const buildTree = (parentId = null) => {
      const children = []
      
      // 添加子文件夹
      folders.value
        .filter(folder => folder.parentId === parentId)
        .forEach(folder => {
          children.push({
            id: folder.id,
            label: folder.name,
            type: 'folder',
            children: buildTree(folder.id)
          })
        })
      
      // 添加文件
      files.value
        .filter(file => file.folderId === parentId)
        .forEach(file => {
          children.push({
            id: file.id,
            label: file.name,
            type: file.type,
            file: file,
            children: []
          })
        })
      
      return children
    }
    
    return [{
      id: 'root',
      label: '我的文件',
      type: 'folder',
      children: buildTree('root')
    }]
  })

  // 操作方法
  const addFile = (fileData) => {
    const newFile = {
      id: generateId(),
      name: fileData.name,
      type: fileData.type,
      size: fileData.size,
      file: fileData.file,
      folderId: currentFolder.value,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    files.value.push(newFile)
    return newFile
  }

  const addFolder = (folderName, parentId = currentFolder.value) => {
    const newFolder = {
      id: generateId(),
      name: folderName,
      parentId: parentId,
      type: 'folder',
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    folders.value.push(newFolder)
    return newFolder
  }

  const deleteFile = (fileId) => {
    const index = files.value.findIndex(file => file.id === fileId)
    if (index > -1) {
      files.value.splice(index, 1)
      if (selectedFile.value && selectedFile.value.id === fileId) {
        selectedFile.value = null
      }
    }
  }

  const deleteFolder = (folderId) => {
    // 递归删除文件夹及其内容
    const deleteRecursive = (id) => {
      // 删除文件夹中的文件
      files.value = files.value.filter(file => file.folderId !== id)
      
      // 删除子文件夹
      const subfolders = folders.value.filter(folder => folder.parentId === id)
      subfolders.forEach(subfolder => deleteRecursive(subfolder.id))
      
      // 删除文件夹本身
      folders.value = folders.value.filter(folder => folder.id !== id)
    }
    
    deleteRecursive(folderId)
  }

  const selectFile = (file) => {
    selectedFile.value = file
  }

  const setCurrentFolder = (folderId) => {
    currentFolder.value = folderId
  }

  const moveFile = (fileId, targetFolderId) => {
    const file = files.value.find(f => f.id === fileId)
    if (file) {
      file.folderId = targetFolderId
      file.updatedAt = new Date()
    }
  }

  const renameFile = (fileId, newName) => {
    const file = files.value.find(f => f.id === fileId)
    if (file) {
      file.name = newName
      file.updatedAt = new Date()
    }
  }

  const renameFolder = (folderId, newName) => {
    const folder = folders.value.find(f => f.id === folderId)
    if (folder) {
      folder.name = newName
      folder.updatedAt = new Date()
    }
  }

  const getFileById = (fileId) => {
    return files.value.find(file => file.id === fileId)
  }

  const getFolderById = (folderId) => {
    return folders.value.find(folder => folder.id === folderId)
  }

  const getFilesByType = (type) => {
    return files.value.filter(file => file.type === type)
  }

  const searchFiles = (keyword) => {
    const lowerKeyword = keyword.toLowerCase()
    return files.value.filter(file => 
      file.name.toLowerCase().includes(lowerKeyword)
    )
  }

  const getFileStats = () => {
    const stats = {
      total: files.value.length,
      images: 0,
      pdfs: 0,
      words: 0,
      totalSize: 0
    }
    
    files.value.forEach(file => {
      stats.totalSize += file.size
      
      switch (file.type) {
        case 'image':
          stats.images++
          break
        case 'pdf':
          stats.pdfs++
          break
        case 'word':
          stats.words++
          break
      }
    })
    
    return stats
  }

  // 工具函数
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type) => {
    const icons = {
      image: 'Picture',
      pdf: 'Document',
      word: 'Document',
      folder: 'Folder'
    }
    
    return icons[type] || 'Document'
  }

  // 导出状态和方法
  return {
    // 状态
    files,
    folders,
    selectedFile,
    currentFolder,
    
    // 计算属性
    currentFolderFiles,
    currentFolderSubfolders,
    fileTree,
    
    // 方法
    addFile,
    addFolder,
    deleteFile,
    deleteFolder,
    selectFile,
    setCurrentFolder,
    moveFile,
    renameFile,
    renameFolder,
    getFileById,
    getFolderById,
    getFilesByType,
    searchFiles,
    getFileStats,
    formatFileSize,
    getFileIcon
  }
})
